const Donate = () => {
  const copyBankDetails = () => {
    const bankDetails = `Bank: Sterling Bank
Account Number: **********
Account Name: The Assurance Brothers International Outreach
Purpose: TABIO CTY 2025 Donation`;

    navigator.clipboard.writeText(bankDetails).then(() => {
      alert('Bank details copied to clipboard!');
    }).catch(() => {
      alert('Failed to copy. Please copy manually.');
    });
  };

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-red-600 via-red-700 to-red-800 py-20 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <div className="mb-8">
              <span className="inline-block bg-white bg-opacity-20 backdrop-blur-sm text-white px-6 py-2 rounded-full text-sm font-medium border border-white border-opacity-30">
                Make a Difference Today
              </span>
            </div>
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              Support Our <span className="text-red-300">Mission</span>
            </h1>
            <p className="text-xl md:text-2xl text-red-100 leading-relaxed max-w-3xl mx-auto">
              Your donation helps us transform lives through education, skills development, and community empowerment.
              Join us in building a brighter future for Nigerian youth.
            </p>

            <div className="mt-12 flex flex-col sm:flex-row gap-6 justify-center">
              <button className="bg-white text-red-600 hover:bg-red-50 font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg">
                <i className="fas fa-heart mr-2"></i>
                Donate Now
              </button>
              <button className="border-2 border-white text-white hover:bg-white hover:text-red-600 font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105">
                <i className="fas fa-info-circle mr-2"></i>
                Learn More
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* TABIO CTY 2025 Special Campaign */}
      <section className="py-16 bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <div className="mb-6">
              <span className="inline-block bg-white bg-opacity-20 backdrop-blur-sm text-white px-6 py-2 rounded-full text-sm font-medium border border-white border-opacity-30">
                Special Campaign 2025
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
              Support TABIO <span className="text-yellow-200">CTY 2025</span>
            </h2>
            <p className="text-xl text-yellow-100 leading-relaxed mb-8">
              Following the tremendous success of CTY 2024 - which surpassed our maiden 2023 edition -
              we're now planning an even more ambitious CTY 2025! Your donation will help us build on
              our proven track record and expand our impact, supporting enhanced skills training,
              career development, and youth empowerment programs across Nigeria.
            </p>

            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 mb-8">
              <h3 className="text-2xl font-bold text-white mb-6">Quick Donation Options</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Option 1 - Bank Transfer */}
                <div className="bg-white rounded-xl p-6 text-center">
                  <h4 className="text-xl font-bold text-red-600 mb-4">Option 1</h4>
                  <div className="space-y-3">
                    <div>
                      <div className="text-sm text-gray-600">Bank</div>
                      <div className="font-bold text-gray-800">Sterling Bank</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Account Number</div>
                      <div className="font-bold text-gray-800 text-lg">**********</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Account Name</div>
                      <div className="font-semibold text-gray-800 text-sm">The Assurance Brothers International Outreach</div>
                    </div>
                  </div>
                </div>

                {/* Option 2 - Alternative Support */}
                <div className="bg-white rounded-xl p-6 text-center">
                  <h4 className="text-xl font-bold text-red-600 mb-4">Option 2</h4>
                  <div className="space-y-3">
                    <div>
                      <div className="font-bold text-gray-800 text-lg">Teenagers Friendly Products</div>
                      <div className="text-sm text-gray-600 mt-2">Support through product purchases or in-kind donations</div>
                    </div>
                    <button className="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 mt-4">
                      <i className="fas fa-envelope mr-2"></i>
                      Contact Us
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center">
              <p className="text-yellow-100 text-lg mb-4">
                <strong>God bless you!!!</strong> Every donation makes a difference in a young person's life.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-white text-yellow-600 hover:bg-yellow-50 font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg">
                  <i className="fas fa-heart mr-2"></i>
                  Donate to CTY 2025
                </button>
                <button className="border-2 border-white text-white hover:bg-white hover:text-yellow-600 font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105">
                  <i className="fas fa-info-circle mr-2"></i>
                  Learn More About CTY
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Impact Statistics Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block bg-red-100 text-red-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
              Our Impact
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Your Donations <span className="text-red-600">Create Change</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              See how your contributions have helped us transform lives and communities across Nigeria over the past decade.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <div className="text-center p-8 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl border border-blue-200 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-graduation-cap text-white text-2xl"></i>
              </div>
              <div className="text-4xl font-bold text-blue-600 mb-2">500+</div>
              <div className="text-gray-700 font-semibold mb-2">Students Supported</div>
              <div className="text-sm text-gray-600">Through SSCE sponsorship and tertiary education financing</div>
            </div>

            <div className="text-center p-8 bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl border border-purple-200 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-hands-helping text-white text-2xl"></i>
              </div>
              <div className="text-4xl font-bold text-purple-600 mb-2">50+</div>
              <div className="text-gray-700 font-semibold mb-2">Women Empowered</div>
              <div className="text-sm text-gray-600">With SME financing and business development support</div>
            </div>

            <div className="text-center p-8 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl border border-green-200 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-users text-white text-2xl"></i>
              </div>
              <div className="text-4xl font-bold text-green-600 mb-2">1000+</div>
              <div className="text-gray-700 font-semibold mb-2">Youth Trained</div>
              <div className="text-sm text-gray-600">Through Catch Them Young and skills development programs</div>
            </div>

            <div className="text-center p-8 bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl border border-orange-200 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-heart text-white text-2xl"></i>
              </div>
              <div className="text-4xl font-bold text-orange-600 mb-2">10+</div>
              <div className="text-gray-700 font-semibold mb-2">Orphanages Supported</div>
              <div className="text-sm text-gray-600">With educational materials, food, and infrastructure</div>
            </div>
          </div>

          <div className="text-center">
            <p className="text-lg text-gray-600 mb-8">
              <strong>Over a decade of impact</strong> - and we're just getting started. Your support helps us reach even more communities.
            </p>
            <button className="bg-red-600 hover:bg-red-700 text-white font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg">
              <i className="fas fa-chart-line mr-2"></i>
              See Detailed Impact Report
            </button>
          </div>
        </div>
      </section>

      {/* Donation Impact Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block bg-green-100 text-green-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
              Your Impact
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              What Your <span className="text-green-600">Donation</span> Can Do
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Every donation, no matter the size, makes a real difference in someone's life.
              Here's how your contribution can create lasting change.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* ₦5,000 Impact */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-l-4 border-blue-500">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-book text-white text-xl"></i>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">₦5,000</div>
                  <div className="text-sm text-gray-600">Monthly Impact</div>
                </div>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">Educational Materials</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <i className="fas fa-check text-blue-500 mr-2"></i>
                  School supplies for 5 students
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-blue-500 mr-2"></i>
                  Textbooks and learning materials
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-blue-500 mr-2"></i>
                  Workshop materials for skills training
                </li>
              </ul>
            </div>

            {/* ₦15,000 Impact */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-l-4 border-purple-500">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-user-graduate text-white text-xl"></i>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">₦15,000</div>
                  <div className="text-sm text-gray-600">Monthly Impact</div>
                </div>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">Skills Training</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <i className="fas fa-check text-purple-500 mr-2"></i>
                  Full skills training for 1 youth
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-purple-500 mr-2"></i>
                  Tools and equipment access
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-purple-500 mr-2"></i>
                  Certificate and career guidance
                </li>
              </ul>
            </div>

            {/* ₦50,000 Impact */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-l-4 border-green-500">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-graduation-cap text-white text-xl"></i>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">₦50,000</div>
                  <div className="text-sm text-gray-600">One-time Impact</div>
                </div>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">SSCE Sponsorship</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <i className="fas fa-check text-green-500 mr-2"></i>
                  Complete SSCE exam fees for 1 student
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-green-500 mr-2"></i>
                  Registration and documentation
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-green-500 mr-2"></i>
                  Academic support and mentoring
                </li>
              </ul>
            </div>

            {/* ₦100,000 Impact */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-l-4 border-red-500">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-hands-helping text-white text-xl"></i>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600">₦100,000</div>
                  <div className="text-sm text-gray-600">One-time Impact</div>
                </div>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">Women Empowerment</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <i className="fas fa-check text-red-500 mr-2"></i>
                  SME startup capital for 1 woman
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-red-500 mr-2"></i>
                  Business training and mentorship
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-red-500 mr-2"></i>
                  Ongoing support and guidance
                </li>
              </ul>
            </div>

            {/* ₦250,000 Impact */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-l-4 border-indigo-500">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-indigo-500 rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-university text-white text-xl"></i>
                </div>
                <div>
                  <div className="text-2xl font-bold text-indigo-600">₦250,000</div>
                  <div className="text-sm text-gray-600">Annual Impact</div>
                </div>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">University Support</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <i className="fas fa-check text-indigo-500 mr-2"></i>
                  One year university tuition
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-indigo-500 mr-2"></i>
                  Academic materials and resources
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-indigo-500 mr-2"></i>
                  Mentorship throughout studies
                </li>
              </ul>
            </div>

            {/* ₦500,000+ Impact */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-l-4 border-yellow-500">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-yellow-500 rounded-xl flex items-center justify-center mr-4">
                  <i className="fas fa-star text-white text-xl"></i>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-600">₦500,000+</div>
                  <div className="text-sm text-gray-600">Major Impact</div>
                </div>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">Program Sponsorship</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <i className="fas fa-check text-yellow-500 mr-2"></i>
                  Sponsor entire Catch Them Young program
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-yellow-500 mr-2"></i>
                  Impact 50+ youth in one program
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-yellow-500 mr-2"></i>
                  Recognition as program partner
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Success Stories Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block bg-blue-100 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
              Success Stories
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Lives <span className="text-blue-600">Transformed</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Meet some of the amazing individuals whose lives have been transformed through the generous support of donors like you.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Success Story 1 */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 border border-blue-200 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-user-graduate text-white text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4 text-center">Adebayo's Journey</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                "Thanks to TABIO's SSCE sponsorship, I was able to complete my secondary education and gain admission
                to study Computer Science at the University of Lagos. Today, I'm a software developer helping other
                young people learn coding skills."
              </p>
              <div className="text-center">
                <span className="inline-block bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                  SSCE Beneficiary → University Graduate
                </span>
              </div>
            </div>

            {/* Success Story 2 */}
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-8 border border-purple-200 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-female text-white text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4 text-center">Fatima's Business</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                "The SME financing I received helped me start my catering business. Now I employ 3 other women
                and can comfortably pay for my children's education. TABIO gave me hope when I had none."
              </p>
              <div className="text-center">
                <span className="inline-block bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                  SME Beneficiary → Business Owner
                </span>
              </div>
            </div>

            {/* Success Story 3 */}
            <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-8 border border-green-200 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-tools text-white text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-4 text-center">Kemi's Skills</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                "I learned fashion design through the Catch Them Young program. The skills I gained have allowed
                me to start my own fashion house. I now train other young people in my community."
              </p>
              <div className="text-center">
                <span className="inline-block bg-green-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                  Skills Training → Entrepreneur
                </span>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <p className="text-lg text-gray-600 mb-8">
              These are just a few of the hundreds of success stories made possible by generous donors.
            </p>
            <button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg">
              <i className="fas fa-book-open mr-2"></i>
              Read More Success Stories
            </button>
          </div>
        </div>
      </section>

      {/* How to Donate Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block bg-red-100 text-red-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
              How to Donate
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Ways to <span className="text-red-600">Support</span> Us
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Choose the donation method that works best for you. Every contribution, no matter the size, makes a difference.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Bank Transfer */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-university text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4 text-center">Bank Transfer</h3>
              <div className="space-y-4 mb-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Account Name</div>
                  <div className="font-semibold text-gray-800">The Assurance Brothers International Outreach</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Bank</div>
                  <div className="font-semibold text-gray-800">Sterling Bank</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">Account Number</div>
                  <div className="font-semibold text-gray-800 text-lg">**********</div>
                </div>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg mb-4">
                <div className="text-center">
                  <div className="text-sm text-blue-600 font-semibold mb-1">Special Campaign</div>
                  <div className="text-blue-800 font-bold">Support TABIO CTY 2024</div>
                  <div className="text-xs text-blue-600 mt-1">Catch Them Young 2024 Program</div>
                </div>
              </div>
              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300">
                <i className="fas fa-copy mr-2"></i>
                Copy Account Details
              </button>
            </div>

            {/* Online Donation */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-2 border-red-200">
              <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-credit-card text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4 text-center">Online Donation</h3>
              <div className="mb-6">
                <p className="text-gray-600 leading-relaxed text-center">
                  Quick and secure online donations through our trusted payment partners.
                  Get instant confirmation and tax receipts.
                </p>
              </div>
              <div className="space-y-3 mb-6">
                <div className="flex items-center justify-center text-sm text-gray-600">
                  <i className="fas fa-check text-green-500 mr-2"></i>
                  Secure payment processing
                </div>
                <div className="flex items-center justify-center text-sm text-gray-600">
                  <i className="fas fa-check text-green-500 mr-2"></i>
                  Instant confirmation
                </div>
                <div className="flex items-center justify-center text-sm text-gray-600">
                  <i className="fas fa-check text-green-500 mr-2"></i>
                  Tax receipt provided
                </div>
              </div>
              <button className="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300">
                <i className="fas fa-heart mr-2"></i>
                Donate Online
              </button>
              <div className="text-center mt-3">
                <span className="inline-block bg-red-100 text-red-600 px-3 py-1 rounded-full text-xs font-medium">
                  Most Popular
                </span>
              </div>
            </div>

            {/* Contact Donation */}
            <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-phone text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4 text-center">Contact Us</h3>
              <div className="mb-6">
                <p className="text-gray-600 leading-relaxed text-center">
                  Prefer to speak with someone directly? Contact our team to discuss donation options,
                  corporate partnerships, or specific program sponsorships.
                </p>
              </div>
              <div className="space-y-3 mb-6">
                <div className="text-center">
                  <div className="text-sm text-gray-600">Email</div>
                  <div className="font-semibold text-gray-800"><EMAIL></div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-600">Phone</div>
                  <div className="font-semibold text-gray-800">+234 ************</div>
                </div>
              </div>
              <button className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300">
                <i className="fas fa-comments mr-2"></i>
                Contact Us
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-r from-red-600 to-red-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Transform a Life <span className="text-red-300">Today</span>
          </h2>
          <p className="text-xl text-red-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Your donation doesn't just provide financial support – it provides hope, opportunity, and a brighter future.
            Join us in our mission to empower Nigerian youth and communities.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
            <button className="bg-white text-red-600 hover:bg-red-50 font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg">
              <i className="fas fa-heart mr-2"></i>
              Donate Now
            </button>
            <a
              href="https://forms.gle/Nxon7HurzpFbtk9y5"
              target="_blank"
              rel="noopener noreferrer"
              className="border-2 border-white text-white hover:bg-white hover:text-red-600 font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 inline-flex items-center justify-center"
            >
              <i className="fas fa-hand-holding-heart mr-2"></i>
              Volunteer Instead
            </a>
          </div>

          <div className="max-w-2xl mx-auto">
            <p className="text-red-100 mb-4">
              <strong>Tax Deductible:</strong> All donations to TABIO are tax-deductible to the extent allowed by law.
            </p>
            <p className="text-red-200 text-sm">
              TABIO is a registered non-profit organization committed to transparency and accountability in all our operations.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Donate;
