import { useState, useEffect } from 'react';

const Hero = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [typewriterText, setTypewriterText] = useState('');
  const [isTyping, setIsTyping] = useState(true);

  // Hero images carousel - mix of 2023 and 2024
  const heroImages = [
    '/tabio-cms-images/tabio cty 2024/grand-finale/group_photograph1.jpg',
    '/tabio-cms-images/cty-golive/0Q1A1762.JPG',
    '/tabio-cms-images/tabio cty 2024/career talk/Career_talk1.jpg',
    '/tabio-cms-images/cty-golive/0Q1A1788.JPG',
    '/tabio-cms-images/tabio cty 2024/grand-finale/award_to_student.jpg',
    '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-21 at 15.36.07.jpg'
  ];

  // Typewriter effect for TABIO full meaning
  const fullMeaning = "The Assurance Brothers' International Outreach";

  useEffect(() => {
    let timeout;
    if (isTyping && typewriterText.length < fullMeaning.length) {
      timeout = setTimeout(() => {
        setTypewriterText(fullMeaning.slice(0, typewriterText.length + 1));
      }, 100); // Typing speed
    } else if (typewriterText.length === fullMeaning.length) {
      // Pause before restarting
      timeout = setTimeout(() => {
        setIsTyping(false);
        setTypewriterText('');
      }, 3000);
    } else {
      // Restart typing
      timeout = setTimeout(() => {
        setIsTyping(true);
      }, 500);
    }

    return () => clearTimeout(timeout);
  }, [typewriterText, isTyping, fullMeaning]);

  // Auto-advance carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        (prevIndex + 1) % heroImages.length
      );
    }, 5000); // Change image every 5 seconds

    return () => clearInterval(interval);
  }, [heroImages.length]);

  const goToSlide = (index) => {
    setCurrentImageIndex(index);
  };

  return (
    <div>
      <div
        className="relative pt-16 pb-32 flex content-center items-center justify-center overflow-hidden"
        style={{
          minHeight: "85vh",
        }}
      >
        {/* Background Images Carousel */}
        {heroImages.map((image, index) => (
          <div
            key={index}
            className={`absolute top-0 w-full h-full bg-center bg-cover transition-opacity duration-1000 ${
              index === currentImageIndex ? 'opacity-100' : 'opacity-0'
            }`}
            style={{
              backgroundImage: `url('${image}')`,
            }}
          >
            <span className="w-full h-full absolute bg-gradient-to-r from-black/70 via-black/50 to-black/70"></span>
          </div>
        ))}

        {/* Content Container */}
        <div className="container relative mx-auto z-10">
            <div className="items-center flex flex-wrap">
              <div className="w-full lg:w-10/12 xl:w-8/12 flex align-items-center justify-content-center px-4 mx-auto text-center">
                <div className="mx-auto">
                  <div className="mb-4">
                    <span className="inline-block bg-green-600/20 backdrop-blur-sm text-green-300 px-4 py-2 rounded-full text-sm font-medium border border-green-400/30">
                      Transforming Lives Since 2004
                    </span>
                  </div>

                  {/* TABIO with Typewriter Effect */}
                  <div className="mb-6">
                    <h1 className="text-white font-bold text-5xl md:text-6xl lg:text-7xl xl:text-8xl leading-tight mb-2">
                      <span className="text-green-400">TABIO</span>
                    </h1>
                    <div className="h-16 md:h-20 flex items-center justify-center">
                      <p className="text-xl md:text-2xl lg:text-3xl text-gray-200 font-light min-h-[2em] flex items-center">
                        {typewriterText}
                        <span className="ml-1 animate-pulse text-green-400">|</span>
                      </p>
                    </div>
                  </div>

                  {/* Simplified Mission Statement */}
                  <p className="text-lg md:text-xl text-gray-300 leading-relaxed max-w-2xl mx-auto mb-8">
                    Empowering African youth through education, skills development, and community outreach.
                  </p>

                  {/* Call to Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <a
                      href="/programs"
                      className="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg"
                    >
                      Explore Our Programs
                    </a>
                    <a
                      href="/cty2024"
                      className="border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105"
                    >
                      View CTY 2024 Success
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Carousel Indicators */}
          <div className="absolute bottom-24 left-1/2 transform -translate-x-1/2 z-10">
            <div className="flex space-x-4">
              {heroImages.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className={`w-4 h-4 rounded-full transition-all duration-300 border-2 ${
                    index === currentImageIndex
                      ? 'bg-green-400 border-green-400 scale-125 shadow-lg'
                      : 'bg-transparent border-white/60 hover:border-white hover:bg-white/20'
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          </div>
          <div
            className="top-auto bottom-0 left-0 right-0 w-full absolute pointer-events-none overflow-hidden"
            style={{ height: "70px" }}
          >
            <svg
              className="absolute bottom-0 overflow-hidden"
              xmlns="http://www.w3.org/2000/svg"
              preserveAspectRatio="none"
              version="1.1"
              viewBox="0 0 2560 100"
              x="0"
              y="0"
            >
              <polygon
                className="text-gray-300 fill-current"
                points="2560 0 2560 100 0 100"
              ></polygon>
            </svg>
          </div>
        </div>
      </div>
    );
}

export default Hero
