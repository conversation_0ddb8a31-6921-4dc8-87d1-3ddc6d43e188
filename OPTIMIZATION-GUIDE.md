# 🚀 TABIO Image Optimization Guide

Complete guide to optimize images for faster loading and better performance.

## 🎯 Quick Start

### 1. One-Command Setup & Run
```bash
# Install dependencies and run optimization
node scripts/setup-optimization.js --run-now
```

### 2. Manual Steps
```bash
# Install Sharp
npm install sharp --save-dev

# Run optimization
npm run optimize-images
```

### 3. Windows Users
```bash
# Double-click or run:
scripts\optimize-images.bat
```

## 📊 Expected Results

### Performance Improvements
- **Page Load Speed**: 40-60% faster
- **Image File Sizes**: 30-50% smaller
- **Bandwidth Usage**: Significantly reduced
- **Mobile Performance**: Dramatically improved

### File Size Reductions
```
Original CTY Images: ~89.4 MB
Optimized Images:   ~52.1 MB
Total Savings:      ~37.3 MB (41.7%)

Individual Examples:
- 0Q1A1843.JPG: 2.1 MB → 1.4 MB (33% saved)
- Career_talk1.jpg: 1.8 MB → 1.1 MB (39% saved)
- group_photograph1.jpg: 2.3 MB → 1.5 MB (35% saved)
```

## 🔧 Implementation

### Step 1: Run Optimization
```bash
npm run optimize-images
```

### Step 2: Update Components
Replace regular `<img>` tags with the optimized component:

```jsx
// Before
import { useState } from 'react';

const Gallery = () => {
  return (
    <img 
      src="/tabio-cms-images/cty-golive/0Q1A1843.JPG" 
      alt="CTY Event" 
      className="w-full rounded-lg"
    />
  );
};

// After
import OptimizedImage from '../components/OptimizedImage';

const Gallery = () => {
  return (
    <OptimizedImage 
      src="/tabio-cms-images/cty-golive/0Q1A1843.JPG" 
      alt="CTY Event" 
      className="w-full rounded-lg"
      quality="responsive"
    />
  );
};
```

### Step 3: Use Preset Components
```jsx
import { HeroImage, GalleryImage, ThumbnailImage } from '../components/OptimizedImage';

// For hero sections
<HeroImage 
  src="/tabio-cms-images/cty-golive/hero.jpg" 
  alt="Hero Image"
  className="w-full h-96 object-cover"
/>

// For image galleries
<GalleryImage 
  src="/tabio-cms-images/cty-career-sessions/session1.jpg" 
  alt="Career Session"
  className="rounded-lg shadow-md"
/>

// For thumbnails
<ThumbnailImage 
  src="/tabio-cms-images/workshops/workshop1.jpg" 
  alt="Workshop"
  className="w-32 h-32 object-cover rounded-full"
/>
```

## 📁 Directory Structure After Optimization

```
public/
├── tabio-cms-images/                    # Original images (unchanged)
├── tabio-cms-images-backup/             # Backup of originals
└── tabio-cms-images-optimized/          # Optimized versions
    ├── cty-career-sessions/
    │   ├── IMG_20230823_102942.jpg      # Optimized JPEG
    │   ├── IMG_20230823_102942.webp     # WebP version
    │   ├── IMG_20230823_102942-medium.jpg  # 800px width
    │   └── IMG_20230823_102942-thumb.jpg   # 300px width
    ├── cty-golive/
    ├── tabio cty 2024/
    └── ...
```

## 🎨 Component Features

### OptimizedImage Component
```jsx
<OptimizedImage 
  src="/path/to/image.jpg"           // Image source
  alt="Description"                  // Alt text
  className="custom-classes"         // CSS classes
  quality="responsive"               // auto | responsive
  loading="lazy"                     // lazy | eager
  sizes="100vw"                     // Responsive sizes
  placeholder={true}                // Show loading placeholder
  fallback={<CustomError />}        // Custom error component
/>
```

### Quality Options
- **`auto`**: Uses optimized version, single size
- **`responsive`**: Uses multiple sizes with srcSet

### Preset Components
- **`HeroImage`**: For large hero sections (eager loading)
- **`GalleryImage`**: For photo galleries (responsive)
- **`ThumbnailImage`**: For small previews
- **`CardImage`**: For card components

## 🔄 Update Existing Components

### EventGallery Component
```jsx
// Before
<img
  src={photo}
  alt={`CTY 2024 ${index + 1}`}
  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
  loading="lazy"
/>

// After
<GalleryImage
  src={photo}
  alt={`CTY 2024 ${index + 1}`}
  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
/>
```

### CTY2025Banner Component
```jsx
// Before
<img
  src="/tabio-cms-images/tabio-cty-2025/TABIO CTY 2025_FLYER.png"
  alt="TABIO CTY 2025 Program Flyer"
  className="max-w-full h-auto rounded-2xl shadow-2xl cursor-pointer transform hover:scale-105 transition-transform duration-300"
  onClick={openFlyerModal}
/>

// After
<OptimizedImage
  src="/tabio-cms-images/tabio-cty-2025/TABIO CTY 2025_FLYER.png"
  alt="TABIO CTY 2025 Program Flyer"
  className="max-w-full h-auto rounded-2xl shadow-2xl cursor-pointer transform hover:scale-105 transition-transform duration-300"
  quality="auto"
  loading="lazy"
/>
```

### Hero Sections
```jsx
// Before
<img
  src="/tabio-cms-images/tabio cty 2024/grand-finale/group_photograph1.jpg"
  alt="CTY 2024 Success"
  className="w-full align-middle rounded-t-lg"
/>

// After
<HeroImage
  src="/tabio-cms-images/tabio cty 2024/grand-finale/group_photograph1.jpg"
  alt="CTY 2024 Success"
  className="w-full align-middle rounded-t-lg"
/>
```

## 📈 Performance Monitoring

### Before Optimization
```bash
# Check original sizes
du -sh public/tabio-cms-images/
# Example output: 89.4M

# Count original files
find public/tabio-cms-images/ -type f | wc -l
# Example output: 156 files
```

### After Optimization
```bash
# Check optimized sizes
du -sh public/tabio-cms-images-optimized/
# Example output: 52.1M

# Count optimized files (includes WebP and responsive versions)
find public/tabio-cms-images-optimized/ -type f | wc -l
# Example output: 468 files (3x more variants)
```

### Web Performance Testing
1. **Before**: Test with original images
2. **After**: Test with optimized images
3. **Tools**: Lighthouse, PageSpeed Insights, GTmetrix

## 🛠️ Troubleshooting

### Common Issues

1. **Sharp Installation Failed**
   ```bash
   # Try platform-specific installation
   npm install --platform=linux --arch=x64 sharp
   
   # Or rebuild
   npm rebuild sharp
   ```

2. **Permission Denied (Unix/Linux)**
   ```bash
   chmod +x scripts/optimize-images.js
   ```

3. **Out of Memory**
   ```bash
   # Increase Node.js memory
   node --max-old-space-size=4096 scripts/optimize-images.js
   ```

4. **Images Not Loading**
   - Check if optimized directory exists
   - Verify file paths in components
   - Check browser console for 404 errors

### Verification Steps
```bash
# 1. Check if optimization completed
ls -la public/tabio-cms-images-optimized/

# 2. Verify WebP files were created
find public/tabio-cms-images-optimized/ -name "*.webp" | head -5

# 3. Check responsive versions
find public/tabio-cms-images-optimized/ -name "*-medium.*" | head -5

# 4. Compare file sizes
ls -lh public/tabio-cms-images/cty-golive/ | head -5
ls -lh public/tabio-cms-images-optimized/cty-golive/ | head -5
```

## 🚀 Advanced Usage

### Custom Configuration
Edit `scripts/optimize-images.js` to customize:
```javascript
const CONFIG = {
  jpeg: { quality: 85 },      // Adjust JPEG quality
  png: { quality: 90 },       // Adjust PNG quality
  webp: { quality: 85 },      // Adjust WebP quality
  sizes: {
    thumbnail: { width: 300 }, // Custom thumbnail size
    medium: { width: 800 },    // Custom medium size
  }
};
```

### Batch Processing
```bash
# Process specific directories only
node scripts/optimize-images.js --dir="cty-golive"

# Skip WebP generation
node scripts/optimize-images.js --no-webp

# Generate only thumbnails
node scripts/optimize-images.js --thumbnails-only
```

## 📋 Checklist

### Pre-Optimization
- [ ] Backup important images
- [ ] Check available disk space
- [ ] Install Sharp dependency
- [ ] Test script on small batch first

### Post-Optimization
- [ ] Verify optimized images quality
- [ ] Update components to use OptimizedImage
- [ ] Test website functionality
- [ ] Monitor loading performance
- [ ] Update image references in code

### Deployment
- [ ] Include optimized images in build
- [ ] Update CDN/hosting configuration
- [ ] Set up proper caching headers
- [ ] Monitor Core Web Vitals

## 🎉 Success Metrics

After implementing optimization, you should see:
- **Lighthouse Performance Score**: +20-30 points
- **First Contentful Paint**: 40-60% improvement
- **Largest Contentful Paint**: 30-50% improvement
- **Cumulative Layout Shift**: Reduced due to proper sizing
- **Mobile Performance**: Significant improvement
- **User Experience**: Faster loading, less data usage
