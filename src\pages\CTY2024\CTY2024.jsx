import { useState } from 'react';
import Lightbox from '../../components/Lightbox';

const CTY2024 = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');

  // 2024 CTY photos organized by category
  const cty2024Photos = {
    careerTalk: [
      '/tabio-cms-images/tabio cty 2024/career talk/Career_talk1.jpg',
      '/tabio-cms-images/tabio cty 2024/career talk/Career_talk2.jpg',
      '/tabio-cms-images/tabio cty 2024/career talk/Career_talk_participants.jpg',
      '/tabio-cms-images/tabio cty 2024/career talk/Guest_speakers_facilitators.jpg',
      '/tabio-cms-images/tabio cty 2024/career talk/IMG_1425.jpg',
      '/tabio-cms-images/tabio cty 2024/career talk/career_talk3.jpg',
      '/tabio-cms-images/tabio cty 2024/career talk/participants.jpg',
      '/tabio-cms-images/tabio cty 2024/career talk/president_engage_student.jpg'
    ],
    grandFinale: [
      '/tabio-cms-images/tabio cty 2024/grand-finale/Augusta.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/Award_to_student2.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/Guest_speaker2.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/award_to_facilitator1.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/award_to_police.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/award_to_student.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/exitement.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/group_photograph1.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/group_photograph2.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/guestspeaker_address_participants.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/participants_present_project.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/pedicure.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/prayer_for_tabio_memebers.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/president-speak.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/president_media_team_lead.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/random.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/studentwith_certificate1.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/studentwith_certificate2.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/studentwith_certificate3.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/studentwith_certificate4.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/studentwith_certificate5.jpg'
    ],
    trainingSessions: [
      '/tabio-cms-images/tabio cty 2024/training sessions/career_session1.jpg',
      '/tabio-cms-images/tabio cty 2024/training sessions/career_session2.jpg',
      '/tabio-cms-images/tabio cty 2024/training sessions/career_session3.jpg',
      '/tabio-cms-images/tabio cty 2024/training sessions/career_session4.jpg',
      '/tabio-cms-images/tabio cty 2024/training sessions/career_session5.jpg',
      '/tabio-cms-images/tabio cty 2024/training sessions/participant_writing.jpg',
      '/tabio-cms-images/tabio cty 2024/training sessions/participants.jpg'
    ]
  };

  const categories = [
    { id: 'all', name: 'All Sessions', count: Object.values(cty2024Photos).flat().length },
    { id: 'careerTalk', name: 'Career Talk', count: cty2024Photos.careerTalk.length },
    { id: 'trainingSessions', name: 'Training Sessions', count: cty2024Photos.trainingSessions.length },
    { id: 'grandFinale', name: 'Grand Finale', count: cty2024Photos.grandFinale.length }
  ];

  const getFilteredPhotos = () => {
    if (selectedCategory === 'all') {
      return Object.values(cty2024Photos).flat();
    }
    return cty2024Photos[selectedCategory] || [];
  };

  const openLightbox = (imageSrc) => {
    setSelectedImage(imageSrc);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 py-20 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
        </div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <div className="mb-8">
              <span className="inline-block bg-white bg-opacity-20 backdrop-blur-sm text-white px-6 py-2 rounded-full text-sm font-medium border border-white border-opacity-30">
                Catch Them Young 2024
              </span>
            </div>
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              CTY <span className="text-blue-300">2024</span> Sessions
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 leading-relaxed max-w-3xl mx-auto">
              Relive the transformative moments from our 2024 Catch Them Young program - 
              from inspiring career talks to hands-on training sessions and the grand finale celebration.
            </p>
          </div>
        </div>
      </section>

      {/* Program Overview */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Program <span className="text-blue-600">Highlights</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              The 2024 Catch Them Young program was a remarkable journey of skill development, 
              career guidance, and youth empowerment that transformed the lives of many young participants.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <div className="text-center p-8 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-users text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Career Talk Sessions</h3>
              <p className="text-gray-600">
                Inspiring career guidance sessions with industry experts and successful professionals 
                sharing their experiences and insights.
              </p>
            </div>

            <div className="text-center p-8 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-graduation-cap text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Training Sessions</h3>
              <p className="text-gray-600">
                Hands-on skill development workshops covering various practical skills 
                and career preparation activities.
              </p>
            </div>

            <div className="text-center p-8 bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-trophy text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Grand Finale</h3>
              <p className="text-gray-600">
                Celebration of achievements with certificate presentations, awards, 
                and recognition of outstanding participants.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Photo Gallery */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
              Event <span className="text-blue-600">Gallery</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Browse through the memorable moments captured during our CTY 2024 sessions. 
              Each photo tells a story of growth, learning, and transformation.
            </p>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 ${
                  selectedCategory === category.id
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'bg-white text-gray-700 border-2 border-gray-200 hover:border-blue-300 hover:text-blue-600'
                }`}
              >
                {category.name}
                <span className="ml-2 text-sm opacity-75">({category.count})</span>
              </button>
            ))}
          </div>

          {/* Photo Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {getFilteredPhotos().map((photo, index) => (
              <div
                key={index}
                className="group relative overflow-hidden rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
                onClick={() => openLightbox(photo)}
              >
                <div className="aspect-square overflow-hidden">
                  <img
                    src={photo}
                    alt={`CTY 2024 ${index + 1}`}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                    loading="lazy"
                  />
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg p-2">
                      <p className="text-white text-sm font-medium text-center">
                        CTY 2024 - {selectedCategory === 'all' ? 'All Sessions' : categories.find(c => c.id === selectedCategory)?.name}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Lightbox */}
      {selectedImage && (
        <Lightbox imageSrc={selectedImage} onClose={closeLightbox} />
      )}
    </div>
  );
};

export default CTY2024;
