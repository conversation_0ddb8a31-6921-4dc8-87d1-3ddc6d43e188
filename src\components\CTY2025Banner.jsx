import { useState } from 'react';

const CTY2025Banner = ({ variant = 'full', showFlyer = true }) => {
  const [showFlyerModal, setShowFlyerModal] = useState(false);

  const registrationUrl = "https://forms.gle/tBnVoWUZmzUanbtj6";

  const handleRegisterClick = () => {
    window.open(registrationUrl, '_blank');
  };

  const openFlyerModal = () => {
    setShowFlyerModal(true);
  };

  const closeFlyerModal = () => {
    setShowFlyerModal(false);
  };

  if (variant === 'compact') {
    return (
      <div className="bg-gradient-to-r from-purple-600 via-purple-700 to-purple-800 text-white py-4 px-6 rounded-xl shadow-lg">
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-center sm:text-left">
            <h3 className="text-xl font-bold mb-1">🎉 CTY 2025 Registration Open!</h3>
            <p className="text-purple-100 text-sm">Join the next generation of empowered youth</p>
          </div>
          <div className="flex gap-3">
            {showFlyer && (
              <button
                onClick={openFlyerModal}
                className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-full text-sm font-semibold transition-all duration-300"
              >
                View Flyer
              </button>
            )}
            <button
              onClick={handleRegisterClick}
              className="bg-yellow-500 hover:bg-yellow-400 text-purple-900 px-6 py-2 rounded-full font-bold text-sm transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              Register Now
            </button>
          </div>
        </div>
        
        {/* Flyer Modal */}
        {showFlyerModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm" onClick={closeFlyerModal}>
            <div className="relative max-w-4xl max-h-full p-4" onClick={(e) => e.stopPropagation()}>
              <button
                onClick={closeFlyerModal}
                className="absolute top-6 right-6 z-10 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full p-3 transition-colors duration-200"
              >
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              <img
                src="/tabio-cms-images/tabio-cty-2025/TABIO CTY 2025_FLYER.png"
                alt="TABIO CTY 2025 Flyer"
                className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
              />
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <section className="relative bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 py-16 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content Side */}
          <div className="text-center lg:text-left">
            <div className="mb-6">
              <span className="inline-block bg-yellow-500/20 backdrop-blur-sm text-yellow-300 px-6 py-2 rounded-full text-sm font-medium border border-yellow-400/30 animate-pulse">
                🚀 Registration Now Open!
              </span>
            </div>
            
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              TABIO <span className="text-yellow-400">CTY 2025</span>
            </h2>
            
            <p className="text-xl md:text-2xl text-purple-100 leading-relaxed mb-8">
              Building on our successful 2023 & 2024 editions, join us for the most ambitious 
              Catch Them Young program yet! Transform your future with cutting-edge skills training.
            </p>

            {/* Key Features */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
              <div className="flex items-center text-purple-100">
                <i className="fas fa-check-circle text-yellow-400 mr-3"></i>
                <span>Advanced Skills Training</span>
              </div>
              <div className="flex items-center text-purple-100">
                <i className="fas fa-check-circle text-yellow-400 mr-3"></i>
                <span>Industry Expert Mentors</span>
              </div>
              <div className="flex items-center text-purple-100">
                <i className="fas fa-check-circle text-yellow-400 mr-3"></i>
                <span>Career Development</span>
              </div>
              <div className="flex items-center text-purple-100">
                <i className="fas fa-check-circle text-yellow-400 mr-3"></i>
                <span>Certificate & Awards</span>
              </div>
            </div>

            {/* Call to Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <button
                onClick={handleRegisterClick}
                className="bg-yellow-500 hover:bg-yellow-400 text-purple-900 font-bold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-xl text-lg"
              >
                <i className="fas fa-user-plus mr-2"></i>
                Register for CTY 2025
              </button>
              
              {showFlyer && (
                <button
                  onClick={openFlyerModal}
                  className="border-2 border-white text-white hover:bg-white hover:text-purple-900 font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105"
                >
                  <i className="fas fa-image mr-2"></i>
                  View Program Flyer
                </button>
              )}
            </div>
          </div>

          {/* Flyer Side */}
          {showFlyer && (
            <div className="text-center">
              <div className="relative inline-block">
                <img
                  src="/tabio-cms-images/tabio-cty-2025/TABIO CTY 2025_FLYER.png"
                  alt="TABIO CTY 2025 Program Flyer"
                  className="max-w-full h-auto rounded-2xl shadow-2xl cursor-pointer transform hover:scale-105 transition-transform duration-300"
                  onClick={openFlyerModal}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
                <button
                  onClick={openFlyerModal}
                  className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-white/90 hover:bg-white text-purple-900 px-4 py-2 rounded-full font-semibold text-sm transition-all duration-300"
                >
                  <i className="fas fa-expand mr-2"></i>
                  View Full Size
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Flyer Modal */}
      {showFlyerModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm" onClick={closeFlyerModal}>
          <div className="relative max-w-6xl max-h-full p-4" onClick={(e) => e.stopPropagation()}>
            <button
              onClick={closeFlyerModal}
              className="absolute top-6 right-6 z-10 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full p-3 transition-colors duration-200"
            >
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <img
              src="/tabio-cms-images/tabio-cty-2025/TABIO CTY 2025_FLYER.png"
              alt="TABIO CTY 2025 Flyer"
              className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
            />
            <div className="absolute bottom-6 left-6 right-6 bg-white/20 backdrop-blur-sm rounded-lg p-4">
              <div className="text-center">
                <p className="text-white font-medium mb-3">Ready to join CTY 2025?</p>
                <button
                  onClick={handleRegisterClick}
                  className="bg-yellow-500 hover:bg-yellow-400 text-purple-900 font-bold py-3 px-6 rounded-full transition-all duration-300"
                >
                  Register Now
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default CTY2025Banner;
