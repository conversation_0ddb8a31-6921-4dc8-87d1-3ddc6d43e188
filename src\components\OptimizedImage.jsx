import { useState, useEffect } from 'react';

const OptimizedImage = ({ 
  src, 
  alt, 
  className = '', 
  sizes = '100vw',
  loading = 'lazy',
  quality = 'auto',
  placeholder = true,
  fallback = null
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState('');

  // Generate optimized image paths
  const generateImagePaths = (originalSrc) => {
    // Check if it's already an optimized path
    if (originalSrc.includes('tabio-cms-images-optimized')) {
      return {
        original: originalSrc,
        webp: originalSrc.replace(/\.(jpg|jpeg|png)$/i, '.webp'),
        medium: originalSrc.replace(/\.(jpg|jpeg|png)$/i, '-medium$1'),
        thumb: originalSrc.replace(/\.(jpg|jpeg|png)$/i, '-thumb$1'),
        basePath: originalSrc
      };
    }

    // Convert regular path to optimized path
    const optimizedBasePath = originalSrc.replace('/tabio-cms-images/', '/tabio-cms-images-optimized/');
    const nameWithoutExt = optimizedBasePath.substring(0, optimizedBasePath.lastIndexOf('.'));
    const ext = optimizedBasePath.substring(optimizedBasePath.lastIndexOf('.'));

    return {
      original: optimizedBasePath,
      webp: `${nameWithoutExt}.webp`,
      medium: `${nameWithoutExt}-medium${ext}`,
      thumb: `${nameWithoutExt}-thumb${ext}`,
      basePath: optimizedBasePath,
      fallback: originalSrc // Original path as fallback
    };
  };

  const imagePaths = generateImagePaths(src);

  // Check if optimized images exist, fallback to original if not
  useEffect(() => {
    const checkImageExists = async (url) => {
      try {
        const response = await fetch(url, { method: 'HEAD' });
        return response.ok;
      } catch {
        return false;
      }
    };

    const selectBestImage = async () => {
      // Try optimized version first
      if (await checkImageExists(imagePaths.original)) {
        setCurrentSrc(imagePaths.original);
      } else if (imagePaths.fallback && await checkImageExists(imagePaths.fallback)) {
        // Fallback to original if optimized doesn't exist
        setCurrentSrc(imagePaths.fallback);
      } else {
        setImageError(true);
      }
    };

    selectBestImage();
  }, [src]);

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const handleImageError = () => {
    // Try fallback to original image if optimized fails
    if (currentSrc !== imagePaths.fallback && imagePaths.fallback) {
      setCurrentSrc(imagePaths.fallback);
    } else {
      setImageError(true);
    }
  };

  // Generate srcSet for responsive images
  const generateSrcSet = () => {
    const srcSetItems = [];
    
    // Add thumbnail for small screens
    if (quality === 'auto' || quality === 'responsive') {
      srcSetItems.push(`${imagePaths.thumb} 300w`);
      srcSetItems.push(`${imagePaths.medium} 800w`);
    }
    
    // Add original for larger screens
    srcSetItems.push(`${currentSrc} 1200w`);
    
    return srcSetItems.join(', ');
  };

  // Placeholder component
  const PlaceholderComponent = () => (
    <div 
      className={`bg-gray-200 animate-pulse flex items-center justify-center ${className}`}
      style={{ minHeight: '200px' }}
    >
      <svg 
        className="w-12 h-12 text-gray-400" 
        fill="currentColor" 
        viewBox="0 0 20 20"
      >
        <path 
          fillRule="evenodd" 
          d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" 
          clipRule="evenodd" 
        />
      </svg>
    </div>
  );

  // Error component
  const ErrorComponent = () => (
    fallback || (
      <div 
        className={`bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center ${className}`}
        style={{ minHeight: '200px' }}
      >
        <div className="text-center text-gray-500">
          <svg 
            className="w-12 h-12 mx-auto mb-2" 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path 
              fillRule="evenodd" 
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" 
              clipRule="evenodd" 
            />
          </svg>
          <p className="text-sm">Image not found</p>
        </div>
      </div>
    )
  );

  // Show placeholder while loading
  if (!currentSrc && !imageError && placeholder) {
    return <PlaceholderComponent />;
  }

  // Show error state
  if (imageError) {
    return <ErrorComponent />;
  }

  // Render optimized image with WebP support
  return (
    <div className="relative">
      {/* Show placeholder until image loads */}
      {!imageLoaded && placeholder && (
        <div className="absolute inset-0 z-10">
          <PlaceholderComponent />
        </div>
      )}
      
      <picture>
        {/* WebP source for modern browsers */}
        <source 
          srcSet={imagePaths.webp} 
          type="image/webp" 
        />
        
        {/* Responsive image with fallback */}
        <img
          src={currentSrc}
          srcSet={quality === 'responsive' ? generateSrcSet() : currentSrc}
          sizes={quality === 'responsive' ? sizes : undefined}
          alt={alt}
          className={`${className} ${!imageLoaded ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
          loading={loading}
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={{
            maxWidth: '100%',
            height: 'auto'
          }}
        />
      </picture>
    </div>
  );
};

// Preset configurations for common use cases
export const HeroImage = (props) => (
  <OptimizedImage 
    {...props} 
    quality="responsive" 
    sizes="100vw"
    loading="eager"
  />
);

export const ThumbnailImage = (props) => (
  <OptimizedImage 
    {...props} 
    quality="responsive" 
    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
  />
);

export const GalleryImage = (props) => (
  <OptimizedImage 
    {...props} 
    quality="responsive" 
    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
  />
);

export const CardImage = (props) => (
  <OptimizedImage 
    {...props} 
    quality="auto" 
    sizes="(max-width: 768px) 100vw, 400px"
  />
);

export default OptimizedImage;
