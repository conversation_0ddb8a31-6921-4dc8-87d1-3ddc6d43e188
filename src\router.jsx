import {createBrowserRouter } from 'react-router-dom';
import App from './App';
import Home from './pages/Home/Home';
import About from './pages/About/About';
import Contact from './pages/Contact/Contact';
import Donate from './pages/Donate/Donate';
import Programs from './pages/Programs/Programs';
import CTY2024 from './pages/CTY2024/CTY2024';
import CTY2023 from './pages/CTY2023/CTY2023';


const routes = createBrowserRouter([
    {
        path:'/',
        element: <App />,
        children:[
            {
                index: true,
                element: <Home/>
            },
            {
                path:'about',
                element: <About/>
            },
            {
                path:'contact',
                element: <Contact/>
            },
            {
                path:'donate',
                element: <Donate/>
            },
            {
                path:'programs',
                element: <Programs/>
            },
            {
                path:'cty2024',
                element: <CTY2024/>
            },
            {
                path:'cty2023',
                element: <CTY2023/>
            },
        ]
    },
])

export default routes;