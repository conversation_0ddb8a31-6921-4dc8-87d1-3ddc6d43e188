import { useState } from 'react';
import Lightbox from './Lightbox';

const EventGallery = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedImage, setSelectedImage] = useState(null);

  // Event photos organized by category
  const eventPhotos = {
    golive: [
      '/tabio-cms-images/cty-golive/0Q1A1762.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1788.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1843.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1841.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1837.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1831.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1824.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1813.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1800.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1778.JP<PERSON>',
      '/tabio-cms-images/cty-golive/0Q1A1741.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1736.JPG'
    ],
    workshops: [
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-21 at 15.36.07.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-22 at 12.00.13.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-23 at 08.47.50.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-23 at 08.52.03.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-23 at 11.16.33.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-24 at 12.20.48.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-24 at 12.27.34.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-30 at 12.54.44.jpg',
      '/tabio-cms-images/Workshops/IMG-20230830-WA0001.jpg',
      '/tabio-cms-images/Workshops/IMG-20230830-WA0002.jpg',
      '/tabio-cms-images/Workshops/IMG-20230830-WA0007.jpg',
      '/tabio-cms-images/Workshops/IMG-20230830-WA0009.jpg'
    ],
    career: [
      '/tabio-cms-images/cty-career-sessions/IMG_20230823_102942.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230828_111850.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230828_112011.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230828_112134.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230829_105750.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230830_111813.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230830_112400.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230831_104017.jpg'
    ],
    cty2024: [
      '/tabio-cms-images/tabio cty 2024/career talk/Career_talk1.jpg',
      '/tabio-cms-images/tabio cty 2024/career talk/Career_talk2.jpg',
      '/tabio-cms-images/tabio cty 2024/career talk/Career_talk_participants.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/group_photograph1.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/Award_to_student2.jpg',
      '/tabio-cms-images/tabio cty 2024/grand-finale/studentwith_certificate1.jpg',
      '/tabio-cms-images/tabio cty 2024/training sessions/career_session1.jpg',
      '/tabio-cms-images/tabio cty 2024/training sessions/participants.jpg'
    ]
  };

  const categories = [
    { id: 'all', name: 'All Events', count: Object.values(eventPhotos).flat().length },
    { id: 'cty2024', name: 'CTY 2024', count: eventPhotos.cty2024.length },
    { id: 'golive', name: 'CTY 2023 Go-Live', count: eventPhotos.golive.length },
    { id: 'workshops', name: 'CTY 2023 Workshops', count: eventPhotos.workshops.length },
    { id: 'career', name: 'CTY 2023 Career Sessions', count: eventPhotos.career.length }
  ];

  const getFilteredPhotos = () => {
    if (selectedCategory === 'all') {
      return Object.values(eventPhotos).flat();
    }
    return eventPhotos[selectedCategory] || [];
  };

  const openLightbox = (imageSrc) => {
    setSelectedImage(imageSrc);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Our Past Events
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Explore the memorable moments from our various programs, workshops, and community outreach events. 
            Each photo tells a story of transformation, learning, and community building.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 ${
                selectedCategory === category.id
                  ? 'bg-green-600 text-white shadow-lg'
                  : 'bg-white text-gray-700 border-2 border-gray-200 hover:border-green-300 hover:text-green-600'
              }`}
            >
              {category.name}
              <span className="ml-2 text-sm opacity-75">({category.count})</span>
            </button>
          ))}
        </div>

        {/* Photo Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {getFilteredPhotos().map((photo, index) => (
            <div
              key={index}
              className="group relative overflow-hidden rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
              onClick={() => openLightbox(photo)}
            >
              <div className="aspect-square overflow-hidden">
                <img
                  src={photo}
                  alt={`TABIO Event ${index + 1}`}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  loading="lazy"
                />
              </div>
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="flex items-center justify-center">
                    <div className="bg-white/20 backdrop-blur-sm rounded-full p-3">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTY Editions Links */}
        <div className="text-center mt-12">
          <div className="mb-6">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">
              Explore CTY <span className="text-green-600">Editions</span>
            </h3>
            <p className="text-gray-600 mb-6">
              Dive deeper into our Catch Them Young program editions with dedicated galleries
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/cty2024"
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              <i className="fas fa-calendar-alt mr-2"></i>
              CTY 2024 Gallery
            </a>
            <a
              href="/cty2023"
              className="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              <i className="fas fa-history mr-2"></i>
              CTY 2023 Gallery
            </a>
          </div>
        </div>
      </div>

      {/* Lightbox */}
      {selectedImage && (
        <Lightbox imageSrc={selectedImage} onClose={closeLightbox} />
      )}
    </section>
  );
};

export default EventGallery;
