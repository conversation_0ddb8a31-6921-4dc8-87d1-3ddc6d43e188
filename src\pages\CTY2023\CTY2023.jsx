import { useState } from 'react';
import Lightbox from '../../components/Lightbox';
import CTY2025Banner from '../../components/CTY2025Banner';

const CTY2023 = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');

  // 2023 CTY photos organized by category
  const cty2023Photos = {
    careerSessions: [
      '/tabio-cms-images/cty-career-sessions/IMG_20230823_102942.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230828_111850.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230828_112011.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230828_112134.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230828_112323.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230828_143337_1.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230829_105750.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230829_112153.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230830_111813.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230830_111842.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230830_112400.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230830_113024.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230831_104017.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230901_110313.jpg',
      '/tabio-cms-images/cty-career-sessions/IMG_20230901_110527.jpg'
    ],
    grandFinale: [
      '/tabio-cms-images/cty-golive/0Q1A1423.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1435.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1544.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1546.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1555.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1581.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1627.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1639.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1640.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1653.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1657.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1688.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1716.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1736.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1741.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1762.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1778.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1788.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1800.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1813.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1824.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1831.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1837.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1841.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1843.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1846.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1849.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1851.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1857.JPG',
      '/tabio-cms-images/cty-golive/0Q1A1865.JPG'
    ],
    workshops: [
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-21 at 15.36.07.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-22 at 12.00.13.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-22 at 14.07.25.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-22 at 17.47.14.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-23 at 08.47.50.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-23 at 08.52.03.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-23 at 08.52.04.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-23 at 08.52.08.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-23 at 08.52.09.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-23 at 08.52.12.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-23 at 11.16.33.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-23 at 11.16.35.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-23 at 12.10.01.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-23 at 12.10.03.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-24 at 12.20.48.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-24 at 12.27.34.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-24 at 12.28.55.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-24 at 14.06.33.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-08-30 at 12.54.44.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-09-01 at 14.03.45.jpg',
      '/tabio-cms-images/Workshops/WhatsApp Image 2023-09-01 at 14.03.46.jpg',
      '/tabio-cms-images/Workshops/IMG-20230830-WA0001.jpg',
      '/tabio-cms-images/Workshops/IMG-20230830-WA0002.jpg',
      '/tabio-cms-images/Workshops/IMG-20230830-WA0007.jpg',
      '/tabio-cms-images/Workshops/IMG-20230830-WA0009.jpg',
      '/tabio-cms-images/Workshops/IMG-20230830-WA0012.jpg'
    ]
  };

  const categories = [
    { id: 'all', name: 'All Sessions', count: Object.values(cty2023Photos).flat().length },
    { id: 'careerSessions', name: 'Career Sessions', count: cty2023Photos.careerSessions.length },
    { id: 'workshops', name: 'Workshops', count: cty2023Photos.workshops.length },
    { id: 'grandFinale', name: 'Grand Finale', count: cty2023Photos.grandFinale.length }
  ];

  const getFilteredPhotos = () => {
    if (selectedCategory === 'all') {
      return Object.values(cty2023Photos).flat();
    }
    return cty2023Photos[selectedCategory] || [];
  };

  const openLightbox = (imageSrc) => {
    setSelectedImage(imageSrc);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-green-600 via-green-700 to-green-800 py-20 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
        </div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <div className="mb-8">
              <span className="inline-block bg-white bg-opacity-20 backdrop-blur-sm text-white px-6 py-2 rounded-full text-sm font-medium border border-white border-opacity-30">
                Catch Them Young 2023
              </span>
            </div>
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              CTY <span className="text-green-300">2023</span> Sessions
            </h1>
            <p className="text-xl md:text-2xl text-green-100 leading-relaxed max-w-3xl mx-auto">
              Explore the memorable moments from our 2023 Catch Them Young program - 
              from career development sessions to hands-on workshops and the grand finale celebration.
            </p>
            
            {/* Edition Navigation */}
            <div className="mt-8 flex justify-center gap-4">
              <a 
                href="/cty2023" 
                className="bg-white text-green-700 px-6 py-3 rounded-full font-semibold shadow-lg"
              >
                2023 Edition
              </a>
              <a 
                href="/cty2024" 
                className="bg-green-800 bg-opacity-50 text-white px-6 py-3 rounded-full font-semibold border border-white border-opacity-30 hover:bg-opacity-70 transition-all duration-300"
              >
                2024 Edition
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Program Overview */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Program <span className="text-green-600">Highlights</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              The 2023 Catch Them Young program was our foundational year that set the standard 
              for youth empowerment and skill development in our community.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <div className="text-center p-8 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-briefcase text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Career Sessions</h3>
              <p className="text-gray-600">
                Professional development sessions with industry mentors providing career guidance 
                and life skills training for young participants.
              </p>
            </div>

            <div className="text-center p-8 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-tools text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Skills Workshops</h3>
              <p className="text-gray-600">
                Hands-on training workshops covering various practical skills including 
                fashion design, computer skills, and entrepreneurship.
              </p>
            </div>

            <div className="text-center p-8 bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-trophy text-white text-2xl"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Grand Finale</h3>
              <p className="text-gray-600">
                Celebration of achievements with certificate presentations, project showcases, 
                and recognition of outstanding participants.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Photo Gallery */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
              Event <span className="text-green-600">Gallery</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Browse through the memorable moments captured during our CTY 2023 sessions. 
              Each photo tells a story of growth, learning, and community building.
            </p>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 ${
                  selectedCategory === category.id
                    ? 'bg-green-600 text-white shadow-lg'
                    : 'bg-white text-gray-700 border-2 border-gray-200 hover:border-green-300 hover:text-green-600'
                }`}
              >
                {category.name}
                <span className="ml-2 text-sm opacity-75">({category.count})</span>
              </button>
            ))}
          </div>

          {/* Photo Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {getFilteredPhotos().map((photo, index) => (
              <div
                key={index}
                className="group relative overflow-hidden rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
                onClick={() => openLightbox(photo)}
              >
                <div className="aspect-square overflow-hidden">
                  <img
                    src={photo}
                    alt={`CTY 2023 ${index + 1}`}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                    loading="lazy"
                  />
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg p-2">
                      <p className="text-white text-sm font-medium text-center">
                        CTY 2023 - {selectedCategory === 'all' ? 'All Sessions' : categories.find(c => c.id === selectedCategory)?.name}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTY 2025 Registration Call to Action */}
      <CTY2025Banner variant="compact" />

      {/* Lightbox */}
      {selectedImage && (
        <Lightbox imageSrc={selectedImage} onClose={closeLightbox} />
      )}
    </div>
  );
};

export default CTY2023;
