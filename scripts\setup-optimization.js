#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up TABIO Image Optimization...\n');

// Check if we're in the right directory
if (!fs.existsSync('package.json')) {
  console.error('❌ package.json not found. Please run this script from the project root directory.');
  process.exit(1);
}

// Function to run commands with proper error handling
const runCommand = (command, description) => {
  console.log(`🔄 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed\n`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    return false;
  }
};

// Check Node.js version
console.log('📋 Checking system requirements...');
try {
  const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
  console.log(`✅ Node.js version: ${nodeVersion}`);
  
  const majorVersion = parseInt(nodeVersion.substring(1).split('.')[0]);
  if (majorVersion < 14) {
    console.warn('⚠️  Warning: Node.js 14+ is recommended for optimal performance');
  }
} catch (error) {
  console.error('❌ Node.js is not installed or not in PATH');
  process.exit(1);
}

// Check npm version
try {
  const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
  console.log(`✅ npm version: ${npmVersion}\n`);
} catch (error) {
  console.error('❌ npm is not available');
  process.exit(1);
}

// Install Sharp if not already installed
console.log('📦 Installing dependencies...');
if (!runCommand('npm install sharp --save-dev', 'Installing Sharp image processing library')) {
  console.log('⚠️  Trying alternative Sharp installation...');
  if (!runCommand('npm install --platform=linux --arch=x64 sharp --save-dev', 'Installing Sharp (alternative method)')) {
    console.error('❌ Failed to install Sharp. Please install manually: npm install sharp --save-dev');
    process.exit(1);
  }
}

// Verify Sharp installation
console.log('🔍 Verifying Sharp installation...');
try {
  require('sharp');
  console.log('✅ Sharp is properly installed\n');
} catch (error) {
  console.error('❌ Sharp verification failed:', error.message);
  console.log('💡 Try running: npm rebuild sharp');
  process.exit(1);
}

// Create scripts directory if it doesn't exist
if (!fs.existsSync('scripts')) {
  fs.mkdirSync('scripts');
  console.log('📁 Created scripts directory');
}

// Make scripts executable (Unix-like systems)
if (process.platform !== 'win32') {
  try {
    execSync('chmod +x scripts/optimize-images.js');
    console.log('✅ Made optimization script executable');
  } catch (error) {
    console.log('⚠️  Could not make script executable (this is okay on Windows)');
  }
}

// Check available disk space
console.log('💾 Checking disk space...');
try {
  const stats = fs.statSync('public/tabio-cms-images');
  if (stats.isDirectory()) {
    // Estimate space needed (original + optimized + backup = ~2.5x original)
    const estimatedSpace = execSync('du -sb public/tabio-cms-images 2>/dev/null || echo "0"', { encoding: 'utf8' });
    const sizeBytes = parseInt(estimatedSpace.split('\t')[0]) || 0;
    const sizeGB = (sizeBytes / (1024 * 1024 * 1024)).toFixed(2);
    const neededGB = (sizeBytes * 2.5 / (1024 * 1024 * 1024)).toFixed(2);
    
    console.log(`📊 Current images size: ~${sizeGB} GB`);
    console.log(`📊 Estimated space needed: ~${neededGB} GB (includes backup and optimized versions)`);
    
    if (sizeBytes > 1024 * 1024 * 1024) { // > 1GB
      console.log('⚠️  Large image collection detected. Optimization may take several minutes.');
    }
  }
} catch (error) {
  console.log('⚠️  Could not estimate disk space requirements');
}

console.log('\n🎉 Setup completed successfully!\n');

console.log('📝 Available commands:');
console.log('  npm run optimize-images    - Run image optimization');
console.log('  npm run optimize          - Shorthand for optimization');
console.log('  node scripts/optimize-images.js - Direct script execution');

if (process.platform === 'win32') {
  console.log('  scripts\\optimize-images.bat - Windows batch file');
}

console.log('\n🚀 Ready to optimize images!');
console.log('\n💡 Next steps:');
console.log('1. Run: npm run optimize-images');
console.log('2. Review optimized images in public/tabio-cms-images-optimized/');
console.log('3. Update your components to use OptimizedImage component');
console.log('4. Test the website performance improvements');

// Ask if user wants to run optimization now
if (process.argv.includes('--run-now')) {
  console.log('\n🔄 Running image optimization now...\n');
  runCommand('npm run optimize-images', 'Image optimization');
} else {
  console.log('\n❓ Run optimization now? Add --run-now flag to auto-run');
  console.log('   Example: node scripts/setup-optimization.js --run-now');
}
