import React from "react";
import { Link, useLocation } from "react-router-dom";

export default function Navbar(props) {
  const [navbarOpen, setNavbarOpen] = React.useState(false);
  const [scrolled, setScrolled] = React.useState(false);
  const location = useLocation();

  // Handle scroll effect
  React.useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      setScrolled(isScrolled);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Check if current route is active
  const isActiveRoute = (path) => {
    return location.pathname === path;
  };

  return (
    <nav
      className={`
        ${props.transparent
          ? "fixed top-0 z-50 w-full transition-all duration-300"
          : "relative bg-white shadow-lg"
        }
        ${props.transparent && scrolled
          ? "bg-white/95 backdrop-blur-md shadow-lg"
          : props.transparent
            ? "bg-transparent"
            : "bg-white"
        }
        flex flex-wrap items-center justify-between px-2 py-2
      `}
    >
      <div className="container px-6 mx-auto flex flex-wrap items-center justify-between">
        {/* Logo and Brand Section */}
        <div className="w-full relative flex justify-between lg:w-auto lg:static lg:block lg:justify-start">
          <Link
            to="/"
            className="flex items-center space-x-3 group transition-all duration-300 hover:scale-105"
          >
            <div className="relative">
              <img
                src="/tabio.svg"
                className="h-12 w-12 transition-transform duration-300 group-hover:rotate-6"
                alt="TABIO Logo"
              />
              <div className="absolute inset-0 bg-green-400 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            </div>
            <div className="flex flex-col">
              <span className={`
                ${props.transparent && !scrolled ? "text-white" : "text-gray-800"}
                text-xl font-bold leading-tight transition-colors duration-300
              `}>
                TABIO
              </span>
              <span className={`
                ${props.transparent && !scrolled ? "text-green-300" : "text-green-600"}
                text-xs font-medium uppercase tracking-wider transition-colors duration-300
              `}>
                Doing Good
              </span>
            </div>
          </Link>

          {/* Mobile Menu Button */}
          <button
            className={`
              ${props.transparent && !scrolled ? "text-white hover:text-green-300" : "text-gray-700 hover:text-green-600"}
              cursor-pointer text-2xl leading-none p-2 border border-solid border-transparent rounded-lg
              bg-transparent block lg:hidden outline-none focus:outline-none transition-all duration-300
              hover:bg-gray-100/20 active:scale-95
            `}
            type="button"
            onClick={() => setNavbarOpen(!navbarOpen)}
            aria-label="Toggle navigation menu"
          >
            <i className={`fas ${navbarOpen ? 'fa-times' : 'fa-bars'} transition-transform duration-300`}></i>
          </button>
        </div>
        {/* Navigation Menu */}
        <div
          className={`
            lg:flex flex-grow items-center justify-end transition-all duration-300 ease-in-out
            ${navbarOpen
              ? "block absolute top-full left-0 right-0 bg-white/95 backdrop-blur-md shadow-xl rounded-b-2xl border-t border-gray-200/50 mt-2 mx-4"
              : "hidden"
            }
            lg:static lg:bg-transparent lg:shadow-none lg:border-none lg:mt-0 lg:mx-0 lg:rounded-none
          `}
        >
          {/* Navigation Links */}
          <ul className="flex flex-col lg:flex-row list-none lg:items-center lg:space-x-2 p-4 lg:p-0">
            {/* Home Link */}
            <li className="flex items-center">
              <Link
                to="/"
                className={`
                  relative px-4 py-3 lg:py-2 rounded-lg font-medium text-sm transition-all duration-300
                  group hover:scale-105 active:scale-95
                  ${isActiveRoute('/')
                    ? 'text-green-600 bg-green-50 lg:bg-transparent'
                    : props.transparent && !scrolled
                      ? 'text-white hover:text-green-300 lg:hover:bg-white/10'
                      : 'text-gray-700 hover:text-green-600 hover:bg-green-50'
                  }
                `}
              >
                <span className="relative z-10 flex items-center">
                  <i className="fas fa-home mr-2 text-sm"></i>
                  Home
                </span>
                {isActiveRoute('/') && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-green-600 rounded-full lg:block hidden"></div>
                )}
              </Link>
            </li>

            {/* About Link */}
            <li className="flex items-center">
              <Link
                to="about"
                className={`
                  relative px-4 py-3 lg:py-2 rounded-lg font-medium text-sm transition-all duration-300
                  group hover:scale-105 active:scale-95
                  ${isActiveRoute('/about')
                    ? 'text-green-600 bg-green-50 lg:bg-transparent'
                    : props.transparent && !scrolled
                      ? 'text-white hover:text-green-300 lg:hover:bg-white/10'
                      : 'text-gray-700 hover:text-green-600 hover:bg-green-50'
                  }
                `}
              >
                <span className="relative z-10 flex items-center">
                  <i className="fas fa-info-circle mr-2 text-sm"></i>
                  About
                </span>
                {isActiveRoute('/about') && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-green-600 rounded-full lg:block hidden"></div>
                )}
              </Link>
            </li>

            {/* Programs Link */}
            <li className="flex items-center">
              <Link
                to="programs"
                className={`
                  relative px-4 py-3 lg:py-2 rounded-lg font-medium text-sm transition-all duration-300
                  group hover:scale-105 active:scale-95
                  ${isActiveRoute('/programs')
                    ? 'text-green-600 bg-green-50 lg:bg-transparent'
                    : props.transparent && !scrolled
                      ? 'text-white hover:text-green-300 lg:hover:bg-white/10'
                      : 'text-gray-700 hover:text-green-600 hover:bg-green-50'
                  }
                `}
              >
                <span className="relative z-10 flex items-center">
                  <i className="fas fa-calendar-alt mr-2 text-sm"></i>
                  Programs
                </span>
                {isActiveRoute('/programs') && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-green-600 rounded-full lg:block hidden"></div>
                )}
              </Link>
            </li>

            {/* Contact Link */}
            <li className="flex items-center">
              <Link
                to="contact"
                className={`
                  relative px-4 py-3 lg:py-2 rounded-lg font-medium text-sm transition-all duration-300
                  group hover:scale-105 active:scale-95
                  ${isActiveRoute('/contact')
                    ? 'text-green-600 bg-green-50 lg:bg-transparent'
                    : props.transparent && !scrolled
                      ? 'text-white hover:text-green-300 lg:hover:bg-white/10'
                      : 'text-gray-700 hover:text-green-600 hover:bg-green-50'
                  }
                `}
              >
                <span className="relative z-10 flex items-center">
                  <i className="fas fa-envelope mr-2 text-sm"></i>
                  Contact
                </span>
                {isActiveRoute('/contact') && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-green-600 rounded-full lg:block hidden"></div>
                )}
              </Link>
            </li>

            {/* CTY 2025 Registration Button */}
            <li className="flex items-center lg:ml-4">
              <a
                href="https://forms.gle/tBnVoWUZmzUanbtj6"
                target="_blank"
                rel="noopener noreferrer"
                className={`
                  relative px-6 py-3 lg:py-2.5 rounded-full font-bold text-sm transition-all duration-300
                  transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl
                  bg-gradient-to-r from-purple-600 to-purple-700 text-white hover:from-purple-700 hover:to-purple-800
                  group overflow-hidden animate-pulse
                `}
              >
                <span className="relative z-10 flex items-center">
                  <i className="fas fa-rocket mr-2 text-sm group-hover:animate-bounce"></i>
                  CTY 2025
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
              </a>
            </li>

            {/* Donate Button */}
            <li className="flex items-center lg:ml-2">
              <Link
                to="donate"
                className={`
                  relative px-6 py-3 lg:py-2.5 rounded-full font-semibold text-sm transition-all duration-300
                  transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl
                  ${props.transparent && !scrolled
                    ? 'bg-white text-green-600 hover:bg-green-50 border-2 border-white'
                    : 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800'
                  }
                  group overflow-hidden
                `}
              >
                <span className="relative z-10 flex items-center">
                  <i className="fas fa-heart mr-2 text-sm group-hover:animate-pulse"></i>
                  Donate
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-green-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  );
}
