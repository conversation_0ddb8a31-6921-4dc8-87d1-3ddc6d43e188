

const CoreValue = () => {
    return (
      <div>
        <section className="relative py-20 bg-white">
          <div
            className="bottom-auto top-0 left-0 right-0 w-full absolute pointer-events-none overflow-hidden -mt-20"
            style={{ height: "80px" }}
          >
            <svg
              className="absolute bottom-0 overflow-hidden"
              xmlns="http://www.w3.org/2000/svg"
              preserveAspectRatio="none"
              version="1.1"
              viewBox="0 0 2560 100"
              x="0"
              y="0"
            >
              <polygon
                className="text-white fill-current"
                points="2560 0 2560 100 0 100"
              ></polygon>
            </svg>
          </div>

          <div className="container mx-auto px-4">
            <div className="items-center flex flex-wrap lg:flex-nowrap gap-8">
              <div className="w-full lg:w-5/12 px-4 mb-8 lg:mb-0">
                <div className="relative group">
                  <img
                    alt="TABIO Core Values"
                    className="max-w-full rounded-2xl shadow-2xl transition-transform duration-300 group-hover:scale-105"
                    src="/tabio-cms-images/cty-golive/0Q1A1788.JPG"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
                </div>
              </div>
              <div className="w-full lg:w-7/12 px-4">
                <div className="lg:pl-8">
                  <div className="text-white p-4 text-center inline-flex items-center justify-center w-20 h-20 mb-8 shadow-xl rounded-full bg-gradient-to-r from-green-600 to-green-700">
                    <i className="fas fa-rocket text-2xl"></i>
                  </div>
                  <h3 className="text-4xl lg:text-5xl font-bold text-gray-800 mb-6">TABIO CORE VALUES</h3>
                  <p className="text-xl leading-relaxed text-gray-600 mb-8">
                    We strongly believe in building a community of young godly
                    and professional individuals by:
                  </p>
                  <ul className="list-none space-y-6">
                    <li className="group">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <span className="text-white p-3 inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-green-600 shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i className="fas fa-graduation-cap text-lg"></i>
                          </span>
                        </div>
                        <div className="ml-4">
                          <h4 className="text-lg font-semibold text-gray-800 mb-2">
                            Supporting out of school children to get back to
                            school
                          </h4>
                          <p className="text-gray-600">
                            Providing educational opportunities and resources to help children return to formal education.
                          </p>
                        </div>
                      </div>
                    </li>
                    <li className="group">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <span className="text-white p-3 inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i className="fas fa-tools text-lg"></i>
                          </span>
                        </div>
                        <div className="ml-4">
                          <h4 className="text-lg font-semibold text-gray-800 mb-2">
                            Organizing programs that help young people learn
                            in-demand skills
                          </h4>
                          <p className="text-gray-600">
                            Equipping youth with practical skills and knowledge for today's job market.
                          </p>
                        </div>
                      </div>
                    </li>
                    <li className="group">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <span className="text-white p-3 inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i className="fas fa-hands-helping text-lg"></i>
                          </span>
                        </div>
                        <div className="ml-4">
                          <h4 className="text-lg font-semibold text-gray-800 mb-2">
                            Running mentorship programs to help the youth
                          </h4>
                          <p className="text-gray-600">
                            Providing guidance and support through experienced mentors and role models.
                          </p>
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
}

export default CoreValue;
