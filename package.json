{"name": "tabiongo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port=5000", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.11.0"}, "devDependencies": {"@fortawesome/fontawesome-free": "^6.4.2", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.30", "react-router-dom": "^6.16.0", "tailwindcss": "^3.3.3", "vite": "^4.4.5"}}